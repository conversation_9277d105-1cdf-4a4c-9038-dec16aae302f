/* General Styles */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* Apply Roboto font to all elements in the dashboard */
*{
  font-family: 'Roboto', sans-serif;
}


html,
body {
  margin: 0px;
  height: 100%;
  padding: 0%;
  width: 100%;
  font-family: "Roboto", sans-serif; /* Use a common, clean font */
  color: #333;
}

button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}

a {
  text-decoration: none;
}

/* Main Content Container */
.content-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - var(--navbar-height));
  box-sizing: border-box;
}

/* Header and Buttons */
.frame-4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  /* background-color: #fff; */
  padding: 15px 20px;
  border-radius: 15px;
  /* box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); */
  flex-wrap: wrap;
  gap: 15px;
}

.frame-9 {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 15px;
  border-radius: 20px !important;
  background-color: #aedf32 !important;
  color: #ffffff !important;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none !important;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
  min-width: 150px;
}

.frame-9:hover {
  background-color: #7cb342 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-2px);
}

.btn-icon {
  width: 18px;
  height: 18px;
}

.btntext {
  white-space: nowrap;
}
.add-button {
  background: none;
  cursor: pointer;
  border: none;
}

/* Filter button specific style - removing redundant styles to ensure inheritance from .frame-9 */
/* .filter-button {
  /* All styles now inherited from .frame-9 */
/* If any specific override is needed for filter button, it should be added here */
/* }

.filter-button:hover {
  /* All styles now inherited from .frame-9 */
/* } */
*/ */

/* Delete button specific style */
.delete-btn {
  background-color: #e57373 !important; /* Red background */
}

/* .delete-btn:hover {
  background-color: #ffffff !important;
} */

/* Modal for Update Data (similar to Add Animal, reusing styles) */
.choose-update-milch {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent overlay */
  display: flex;
  flex-direction: column; /* Changed to column for better modal flow */
  align-items: center; /* Center horizontally */
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 1000; /* Ensure it's on top of other content */
  /* animation: fadeIn 0.3s ease; /* Add animation if desired, but not in original ingredient modal css */
}

.modal {
  background-color: white;
  border-radius: 8px; /* From ingredient modal */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* From ingredient modal */
  width: 90%;
  max-width: 600px; /* From ingredient modal */
  max-height: 90vh;
  overflow-y: auto; /* From ingredient modal */
  padding: 20px; /* Adjust padding for consistency with header/body */
  position: relative; /* Ensure z-index works correctly relative to overlay */
}

.close-btn {
  background: none;
  border: none;
  font-size: 10px;
  cursor: pointer;
  color: #666;
  width: 20px; /* From ingredient modal */
  height: 20px; /* From ingredient modal */
  display: flex; /* From ingredient modal */
  align-items: center; /* From ingredient modal */
  justify-content: center; /* From ingredient modal */
  border-radius: 50%; /* From ingredient modal */
  transition: all 0.3s ease; /* From ingredient modal */
  position: absolute; /* To position it top-right */
  top: 10px; /* Adjust as needed */
  right: 10px; /* Adjust as needed */
  z-index: 1001; /* Ensure it's above modal content */
}

.close-btn:hover {
  background-color: #f5f5f5; /* From ingredient modal */
  color: #ff5252; /* From ingredient modal */
}

.choose-update-milch .frame-5 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px; /* Add margin below header */
  padding: 0;
  width: auto;
  height: auto;
  position: static;
  left: auto;
  /* border-bottom: 1px solid #eee; From ingredient modal */
  padding-bottom: 15px; /* Add padding below border */
}

.update {
  position: relative;
  width: 40px;
  height: 40px;
}

.text-wrapper-5 {
  font-weight: 600;
  color: #0b291a;
  font-size: 20px; /* From ingredient modal h2 */
  white-space: nowrap;
  margin: 0; /* Remove default margin */
}

.choose-update-milch .frame-6 {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  align-items: center;
  position: static;
  top: auto;
  gap: 20px;
}

.row {
  width: 95%;
  padding-top: 4.2%;
  display: flex;
  justify-content: space-between;
}

.frame-7 {
  display: flex;
  align-items: center;
  flex-direction: row;
  width: 100%;
}
.selected-items-container {
  width: 100%;
  border: none;
}
.remove-button {
  width: 30px;
  height: 30px;
  border: none;
  background: none;
  position: relative;
  top: 18px;
}

.text-wrapper-6 {
  display: flex;
  flex-direction: row;
  margin-bottom: 8px; /* From ingredient modal label */
  font-weight: 500; /* From ingredient modal label */
  color: #333; /* From ingredient modal label */
  font-size: 14px; /* From ingredient modal label */
  position: static; /* Remove absolute positioning */
  /* Remove fixed width */
  left: auto; /* Remove fixed positioning */

  line-height: normal; /* Reset line height */
}

.data-filled,
.textarea {
  width: 100%; /* Full width */
  padding: 10px 12px; /* From ingredient modal input */
  border: 1px solid #ddd; /* From ingredient modal input */
  border-radius: 34px; /* From ingredient modal input */
  font-size: 14px; /* From ingredient modal input */
  transition: border-color 0.3s ease; /* From ingredient modal input */
  background-color: #f1f1f1; /* Keep existing background if preferred */
  position: static; /* Remove absolute positioning */
  left: auto; /* Remove fixed positioning */
  height: auto; /* Allow height to adjust */
}

.frame-11 {
  position: relative;
  height: 55px;
  width: 45%;
  font-size: 16px;
  display: flex;
  gap: 10%;
  justify-content: center;
  left: 40%;
  background-color: #f1f1f1;
  border-radius: 12px;
}

.frame-111 {
  position: relative;
  height: 55px;
  width: 50%;
  font-size: 16px;
  border-radius: 12px;
  display: flex;
  gap: 10%;
  justify-content: center;
  left: 20%;
  top: 20%;
  background-color: #f1f1f1;
}

.radio-group {
  display: flex; /* Use flex to align radios horizontally */
  gap: 15px; /* Spacing between radio options */
  width: 100%; /* Take full width */
  justify-content: flex-start; /* Align to start */
}

.raddiv {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start; /* Align individual radio text to start */
  padding-left: 0; /* Remove any previous padding */
}

.radio {
  width: 17px;
  height: 17px;
  background: #e3e4e4;
  margin-right: 5px;
  accent-color: black;
}

.textarea {
  resize: vertical; /* From ingredient modal textarea */
  min-height: 80px; /* From ingredient modal textarea */
}

.choose-update-milch .frame-8 {
  display: flex;
  align-items: center;
  padding: 0px 10px;
  position: relative;
  width: 100%;
  justify-content: flex-end; /* Align to end for save button */
  margin-top: 20px; /* Add margin to separate from form */
}

/* Modal Save Button */
.modal-save-btn {
  flex-grow: 0;
  width: 190px;
  height: 50px;
  padding: 10px 20px;
  border-radius: 4px;
  background-color: #8bc34a;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.modal-save-btn:hover {
  background-color: #7cb342;
  box-shadow: none;
  transform: none;
}

/* Modal Delete Confirmation Buttons */
.delete-cancel-btn {
  background-color: #e0e0e0;
  color: #555;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-cancel-btn:hover {
  background-color: #d0d0d0;
}

.delete-confirm-btn {
  background-color: #32cd32 !important; /* Corrected to red with !important */
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3) !important; /* Added shadow for consistency with newborn delete confirm */
}

.delete-confirm-btn:hover {
  background-color: #d32f2f !important;
  box-shadow: 0 4px 10px rgba(244, 67, 54, 0.5) !important;
}

/* Search Input and Filter Dropdown */
.search-container {
  flex-grow: 1;
  max-width: 300px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 18px;
  height: 18px;
  color: #8bc34a;
  z-index: 1;
}

.search-field {
  padding: 10px 12px 10px 40px;
  border-radius: 40px;
  width: 400px;
  height: 45px;
  border: 1px solid #ddd;
  background: rgba(92, 179, 56, 0.25);
  font-size: 14px;
}

.search-field::placeholder {
  color: #9e9e9e;
}

.search-field:focus {
  outline: none;
  border-color: #8bc34a;
  box-shadow: 0 0 0 3px rgba(139, 195, 74, 0.2);
}

.filter-dropdown-container {
  position: relative;
}

.filter-dropdown-menu {
  position: absolute;

  top: 100%;
  left: 0;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 8px 0;
  min-width: 150px;
  display: none;
  z-index: 1000;
}

.filter-dropdown-container.active .filter-dropdown-menu {
  display: block;
  background-color: #AEDF32;
  color: #FFFFFF;
}

.filter-option {
  padding: 8px 16px;
  font-size: 14px;
}

.filter-option:hover {
  background-color: #f5f5f5;
  color: #AEDF32;

}
.x {
  display: flex;
  flex-direction: row;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 22px;
  font-weight: 500;
}
/* Table Styles */
.table-container {
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  overflow-x: auto;
  padding: 20px;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 15px;
}

th,
td {
  padding: 15px;
  text-align: left;

}

th {
  background-color: #f8f8f8;
  font-weight: 600;
  color: #555;
  text-transform: uppercase;
  font-size: 14px;
}

.table-container tbody tr:hover {
  background-color: #f5f5f5;
}

.table-container input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #8bc34a;
  cursor: pointer;
}

/* Modal Styles */
.modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-sizing: border-box;
}
.modaldelete {
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 514px;
  height: 299px;
  max-height: 599px;
  overflow-y: auto;
  position: relative;
  box-sizing: border-box;
}

.modale {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 550px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-sizing: border-box;
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background-color: #f5f5f5;
  color: #ff5252;
}

.frame-5 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 25px;
/*
  border-bottom: 1px solid #eee; */
  margin-bottom: 0;
  gap: 15px;
}
/* .frame-5 {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
  justify-content: flex-start;
} */
.modal-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  position: relative;
}

.text-wrapper-5 {
  font-weight: 600;
  color: #333;
  font-size: 20px;
  white-space: nowrap;
}

.frame-6 {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  align-items: center;
  position: static;
  top: auto;

  gap: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  width: 100%;
}

.form-group {
  flex: 1 1 0;
  min-width: 120px;
  display: flex;
  flex-direction: column;
}

.data-filled {
  height: 40px;
  border-radius: 8px;
  border: 1px solid #ddd;
  padding: 0 12px;
  font-size: 15px;
  background: #f7f7f7;
  margin-top: 4px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #333;
}

.form-flex {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 10px;
}

.form-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  /* background: rgba(128, 128, 128, 0.10); */

  border-radius: 16px;
}

.form-group.full-width {
  grid-column: 1 / -1;
  margin-bottom: 20px;
}

/* Style for all form elements in the popup */

.form-group .textarea,
.form-group .radio-group {
  border-radius: 16px;
  display: flex;
  width: 190px;
  height: 60px;

  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border: 1px solid #ddd;
  background: rgba(128, 128, 128, 0.1);
}
.form-group .data-filledd {
  border-radius: 16px;
  display: flex;
  width: 447px;
  height: 60px;

  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border: 1px solid #ddd;
  background: rgba(128, 128, 128, 0.1);
}


/* Label styles */
.text-wrapper-6 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  width: 100px;
  /* text-align: right; */
}

/* Specific styles for different input types */
.form-group .data-filled {
  font-size: 14px;
}

.form-group .textarea {
  min-height: 60px;
  resize: vertical;
}

.form-group .radio-group {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  padding: 0;
}

.raddiv {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Focus states */
.form-group .data-filled:focus,
.form-group .textarea:focus {
  outline: none;
  /* border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2); */
}

/* Form grid layout */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  width: 100%;
}
/* .form-grid{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
} */

/* Full width elements */

.form-group.full-width .textarea,
.form-group.full-width .radio-group {
  width: 82%;
}
.form-group.full-width .data-filled {
  width: 60%;
  height: 50px;
}
radio-group {
  width: 100%;
}

/* Special handling for full-width type field */
.form-group.full-width {
  display: flex;
  flex-direction: row;
}

.form-group.full-width .text-wrapper-6 {
  text-align: left;
  margin-bottom: 8px;
}

.frame-8 {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding: 0;
  width: 100%;
}

.show-data-btn {
  position: fixed;
  left:58%;

  width: 200px;
  height: 62px;
  background-color:#AEDF32;
  color: #ffffff;

  padding: 12px 20px;
  border-radius: 20px;
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* .show-data-btn:hover {
  background-color: #455a64;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
} */

/* Delete Modal */
.delete-modal {
  background-color: #fff;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  width: 450px;
  max-width: 90%;
  text-align: center;
}

.delete-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 25px;
}

.delete-icon {
  width: 60px;
  height: 60px;
  color: #f44336;
}

.delete-text {
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.delete-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.delete-cancel-btn,
.delete-confirm-btn {
  padding: 12px 25px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
}

.delete-cancel-btn {
  background-color: #32cd32;
  color: #fff;
}

.delete-confirm-btn {
  background-color: #32cd32; /* Corrected to red */
  color: #fff;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

/* .delete-confirm-btn:hover {
  background-color: #d32f2f;
  box-shadow: 0 4px 10px rgba(244, 67, 54, 0.5);
} */

/* Media queries for responsiveness */
@media (max-width: 768px) {
  .frame-4 {
    flex-direction: column;
    align-items: stretch;
  }

  .frame-9,
  .search-container,
  .filter-button,
  .delete-btn {
    width: 100%;
    max-width: none;
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .modal {
    padding: 25px;
    width: 95%;
  }

  .frame-5 {
    flex-direction: column;
    text-align: center;
  }

  .modal-icon {
    margin-bottom: 10px;
  }

  .modal-save-btn {
    width: 100%;
  }
}

.double-left1 {

  width: 18px;
  height: 18px;
  filter: invert(0%);
  /* filter: invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0%)
    contrast(100%); Makes SVG black */
}


