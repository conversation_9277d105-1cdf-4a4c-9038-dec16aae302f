import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import * as signalR from '@microsoft/signalr';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface NotificationItem {
  id?: number;
  title: string;
  message: string;
  type?: 'info' | 'warning' | 'success' | 'error' | 'feed' | 'animal' | 'vaccine';
  createdAt: string;
  timestamp?: Date;
  isRead: boolean;
}
@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = environment.apiUrl + '/api/Accounts';
  private hubConnection!: signalR.HubConnection;
  private notificationsSubject = new BehaviorSubject<NotificationItem[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);
  public notifications$ = this.notificationsSubject.asObservable();
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadInitialNotifications();
  }

  public startConnection(): void {
    console.log('Starting SignalR connection...');
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl('https://localhost:7174/notificationHub') // ✅ لازم نفس البورت و https
      .withAutomaticReconnect()
      .build();

    this.hubConnection
      .start()
      .then(() => {
        console.log('✅ SignalR Connected successfully');
        console.log('Connection state:', this.hubConnection.state);
      })
      .catch(err => {
        console.error('❌ Error connecting SignalR:', err);
        console.log('Connection state:', this.hubConnection.state);
      });

    // Add connection event handlers
    this.hubConnection.onclose((error) => {
      console.log('SignalR connection closed:', error);
    });

    this.hubConnection.onreconnecting((error) => {
      console.log('SignalR reconnecting:', error);
    });

    this.hubConnection.onreconnected((connectionId) => {
      console.log('SignalR reconnected:', connectionId);
    });

    this.hubConnection.on('ReceiveNotification', (data) => {
      console.log('🔔 New notification received:', data);
      console.log('Type of data:', typeof data);
      console.log('Data structure:', JSON.stringify(data, null, 2));

      let notification: NotificationItem;

      // Handle different data structures from backend
      if (data && data.notification) {
        // If data has notification property
        notification = this.formatNotification(data.notification);
        console.log('✅ Formatted notification from data.notification:', notification);
      } else if (data && (data.title || data.message)) {
        // If data is the notification itself
        notification = this.formatNotification(data);
        console.log('✅ Formatted notification from data directly:', notification);
      } else {
        console.error('❌ Invalid notification data structure:', data);
        return;
      }

      const current = this.notificationsSubject.value;
      console.log('Current notifications count:', current.length);
      this.notificationsSubject.next([notification, ...current]);
      this.updateUnreadCount();
      console.log('✅ Notification added to list');
    });
  }

  public stopConnection(): void {
    if (this.hubConnection) {
      this.hubConnection.stop();
    }
  }

  // Local notification methods
  addNotification(notification: Omit<NotificationItem, 'id' | 'createdAt' | 'isRead'>): void {
    const newNotification: NotificationItem = {
      ...notification,
      id: Date.now(),
      createdAt: new Date().toISOString(),
      timestamp: new Date(),
      isRead: false
    };

    const current = this.notificationsSubject.value;
    this.notificationsSubject.next([newNotification, ...current]);
    this.updateUnreadCount();
  }

  markAsRead(notificationId: number): void {
    const current = this.notificationsSubject.value;
    const updated = current.map(n =>
      n.id === notificationId ? { ...n, isRead: true } : n
    );
    this.notificationsSubject.next(updated);
    this.updateUnreadCount();
  }

  markAllAsRead(): void {
    const current = this.notificationsSubject.value;
    const updated = current.map(n => ({ ...n, isRead: true }));
    this.notificationsSubject.next(updated);
    this.updateUnreadCount();
  }

  // Helper methods for specific notification types
  addFeedNotification(title: string, message: string): void {
    this.addNotification({ title, message, type: 'feed' });
  }

  addAnimalNotification(title: string, message: string): void {
    this.addNotification({ title, message, type: 'animal' });
  }

  addVaccineNotification(title: string, message: string): void {
    this.addNotification({ title, message, type: 'vaccine' });
  }

  addSuccessNotification(title: string, message: string): void {
    this.addNotification({ title, message, type: 'success' });
  }

  addWarningNotification(title: string, message: string): void {
    this.addNotification({ title, message, type: 'warning' });
  }

  addErrorNotification(title: string, message: string): void {
    this.addNotification({ title, message, type: 'error' });
  }

  private loadInitialNotifications(): void {
    // Sample notifications
    const initialNotifications: NotificationItem[] = [
      {
        id: 1,
        title: 'تحديث العلف',
        message: 'تم إضافة علف جديد بنجاح',
        type: 'feed',
        createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        isRead: false
      },
      {
        id: 2,
        title: 'تطعيم الحيوانات',
        message: 'حان موعد تطعيم 5 حيوانات',
        type: 'vaccine',
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        isRead: false
      },
      {
        id: 3,
        title: 'إنتاج الألبان',
        message: 'تم تسجيل إنتاج اليوم بنجاح',
        type: 'success',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        isRead: true
      },
      {
        id: 4,
        title: 'تحذير',
        message: 'انخفاض مستوى العلف في المخزن',
        type: 'warning',
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        isRead: false
      }
    ];

    this.notificationsSubject.next(initialNotifications);
    this.updateUnreadCount();
  }

  private updateUnreadCount(): void {
    const current = this.notificationsSubject.value;
    const unreadCount = current.filter(n => !n.isRead).length;
    this.unreadCountSubject.next(unreadCount);
  }

  private formatNotification(data: any): NotificationItem {
    return {
      id: data.id || Date.now(),
      title: data.title || 'إشعار جديد',
      message: data.message || '',
      type: data.type || 'info',
      createdAt: data.createdAt || new Date().toISOString(),
      timestamp: data.timestamp ? new Date(data.timestamp) : new Date(),
      isRead: data.isRead || false
    };
  }

  // API Methods
  loadNotificationsFromAPI(): Observable<NotificationItem[]> {
    return this.http.get<NotificationItem[]>(`${this.apiUrl}/notifications`);
  }

  markAsReadAPI(notificationId: number): Observable<any> {
    return this.http.put(`${this.apiUrl}/notifications/${notificationId}/read`, {});
  }

  markAllAsReadAPI(): Observable<any> {
    return this.http.put(`${this.apiUrl}/notifications/mark-all-read`, {});
  }

  // Method to switch from local to API data
  loadFromAPI(): void {
    this.loadNotificationsFromAPI().subscribe({
      next: (notifications) => {
        console.log('Loaded notifications from API:', notifications);
        this.notificationsSubject.next(notifications);
        this.updateUnreadCount();
      },
      error: (error) => {
        console.error('Failed to load notifications from API:', error);
        console.log('Using local notifications as fallback');
        // Keep using local data as fallback
      }
    });
  }

  // Test method to simulate backend notification
  testBackendNotification(): void {
    console.log('Testing backend notification...');
    const testNotification: NotificationItem = {
      id: Date.now(),
      title: 'اختبار من الباك إند',
      message: 'هذا اشعار تجريبي من السيرفر',
      type: 'info',
      createdAt: new Date().toISOString(),
      timestamp: new Date(),
      isRead: false
    };

    // Simulate receiving from SignalR
    const current = this.notificationsSubject.value;
    this.notificationsSubject.next([testNotification, ...current]);
    this.updateUnreadCount();
    console.log('Test notification added');
  }
}
