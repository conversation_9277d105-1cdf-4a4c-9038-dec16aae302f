import { Injectable } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { BehaviorSubject } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface NotificationItem {
  title: string;
  message: string;
  createdAt: string;
  isRead: boolean;
}
@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = environment.apiUrl + '/api/Accounts';
  private hubConnection!: signalR.HubConnection;
  private notificationsSubject = new BehaviorSubject<any[]>([]);
  public notifications$ = this.notificationsSubject.asObservable();

  constructor() {}

  public startConnection(): void {
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl( this.apiUrl + 'notificationHub') // عدّل الرابط حسب سيرفرك
      .withAutomaticReconnect()
      .build();

    this.hubConnection
      .start()
      .then(() => console.log('SignalR Connected'))
      .catch(err => console.log('Error connecting SignalR: ', err));

    this.hubConnection.on('ReceiveNotification', (data) => {
      console.log('New notification received:', data);
      const current = this.notificationsSubject.value;
      this.notificationsSubject.next([...current, data.notification]);
    });
  }

  public stopConnection(): void {
    if (this.hubConnection) {
      this.hubConnection.stop();
    }
  }
}
