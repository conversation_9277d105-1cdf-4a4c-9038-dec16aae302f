<div class="auth-container">
  <div class="auth-content">
    <div class="auth-form-container">
      <div class="logo">
        <img src="assets/images/icons/logo.png" alt="">
      </div>

      <form [formGroup]="restPasswordForm" (ngSubmit)="onSubmit()" class="auth-form">
        <h2 class="auth-title">Create New Password</h2>
        <p class="form-description">Your new password must different from previous used password</p>

        <div class="form-group">
          <label for="newPassword">
            <i class="fas fa-lock"></i> New Password
          </label>
          <input
            type="password"
            id="newPassword"
            formControlName="newPassword"
            placeholder="Password"
            [ngClass]="{'invalid': restPasswordForm.get('newPassword')?.invalid && restPasswordForm.get('newPassword')?.touched}"
          >
          <div class="error-message" *ngIf="restPasswordForm.get('newPassword')?.invalid && restPasswordForm.get('newPassword')?.touched">
            <span *ngIf="restPasswordForm.get('newPassword')?.errors?.['required']">Password is required</span>
            <span *ngIf="restPasswordForm.get('newPassword')?.errors?.['minlength']">Must be at least 6 characters</span>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">
            <i class="fas fa-lock"></i> Confirm Password
          </label>
          <input
            type="password"
            id="confirmPassword"
            formControlName="confirmPassword"
            placeholder="Password"
            [ngClass]="{'invalid': (restPasswordForm.get('confirmPassword')?.invalid && restPasswordForm.get('confirmPassword')?.touched) || restPasswordForm.errors?.['passwordsMismatch']}"
          >
          <div class="error-message" *ngIf="restPasswordForm.get('confirmPassword')?.invalid && restPasswordForm.get('confirmPassword')?.touched">
            <span *ngIf="restPasswordForm.get('confirmPassword')?.errors?.['required']">Confirm password is required</span>
          </div>
          <div class="error-message" *ngIf="restPasswordForm.errors?.['passwordsMismatch'] && restPasswordForm.get('confirmPassword')?.touched">
            Passwords do not match
          </div>
        </div>

        <div class="error-message main-error" *ngIf="errorMessage">
          {{ errorMessage }}
        </div>

        <div class="success-message" *ngIf="successMessage">
          {{ successMessage }}
        </div>

        <button type="submit" class="submit-btn" [disabled]="isLoading">
          <i class="fas fa-spinner fa-spin" *ngIf="isLoading"></i>
          {{ isLoading ? 'Resetting...' : 'Reset Password' }}
        </button>

        <div class="auth-footer">
          <p>Remember your password? <a routerLink="/login" class="login-link">Back to Login</a></p>
        </div>
      </form>
    </div>

    <div class="auth-image">
      <img class="unsplash" src="assets/images/about.png" />
    </div>
  </div>
</div> 