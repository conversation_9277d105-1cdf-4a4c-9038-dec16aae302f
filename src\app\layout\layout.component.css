/* Main layout styles */
:root {
  --sidebar-width: 240px;
  --navbar-height: 60px;
  --primary-color: #1e1e2d;
  --secondary-color: #1a1a27;
  --accent-color: #8bc34a;
  --text-light: #ffffff;
  --text-dark: #333333;
  --text-muted: #6c7293;
  --border-color: #e0e0e0;
  --bg-light: #f5f8fa;
}
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
 font-family: 'Roboto', sans-serif;
}





html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: #f5f5f5;
}

.app-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;
}

/* Sidebar styles */
.sidebar {
  width: var(--sidebar-width);
  height: 100vh;
  background-color: #1e1e2d;
  color: var(--text-light);
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  overflow-y: auto;
  padding: 0;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: block !important;
  visibility: visible !important;
}

.sidebar::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

.sidebar .logo {
  padding: 25px 20px;
  margin-bottom: 10px;
  text-align: center;
  background-color: #1a1a27;
  position: relative;
}

.sidebar .logo-icon {
  font-size: 24px;
  color: #8bc34a;
  margin-bottom: 10px;
  display: inline-block;
}

.sidebar .logo h2 {
  font-size: 28px;
  margin-bottom: 5px;
  color: rgb(255, 255, 255);
  font-weight: 700;
  letter-spacing: 1px;
}

.sidebar .logo p {
  font-size: 12px;
  color: #565674;
  letter-spacing: 0.5px;
}

.menu-category {
  padding: 10px 25px;
  color: #565674;
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-top: 10px;
}

.badge {
  background-color: rgba(139, 195, 74, 0.2);
  color: #8bc34a;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: auto;
}

.badge.new {
  background-color: rgba(255, 82, 82, 0.2);
  color: #ff5252;
}

.sidebar-footer {
  margin-top: 20px;
  padding: 15px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.sidebar nav {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin-bottom: 10px;
}

.sidebar nav a {
  padding: 12px 25px;
  color: #92929f;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  margin: 2px 0;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.sidebar nav a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: #8bc34a;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.sidebar nav a i {
  margin-right: 15px;
  font-size: 16px;
  width: 20px;
  text-align: center;
  color: #6d6d80;
  transition: all 0.3s ease;
}

.sidebar nav a:hover {
  color: #ffffff;
  background-color: #1a1a27;
}

.sidebar nav a:hover i {
  color: #8bc34a;
}

.sidebar nav a.active {
  color: #ffffff;
  background-color: #1a1a27;
}

.sidebar nav a.active::before {
  transform: scaleY(1);
}

.sidebar nav a.active i {
  color: #8bc34a;
}

/* Add a subtle hover effect for menu items */
.sidebar nav a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: rgba(139, 195, 74, 0.05);
  transition: height 0.3s ease;
  z-index: -1;
}

.sidebar nav a:hover::after {
  height: 100%;
}

.sidebar-footer {
  margin-top: auto;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  padding-top: 15px;
}

.sidebar-footer a {
  padding: 12px 25px;
  color: #92929f;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 14px;
}

.sidebar-footer a i {
  margin-right: 15px;
  font-size: 16px;
  width: 20px;
  text-align: center;
  color: #6d6d80;
  transition: all 0.3s ease;
}

.sidebar-footer a:hover {
  color: #ffffff;
}

.sidebar-footer a:hover i {
  color: #8bc34a;
}

/* Main content area */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #f5f8fa;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  width: calc(100% - var(--sidebar-width));
}

/* Navbar styles */
.navbar {
  height: var(--navbar-height);
  background-color: white;
  box-shadow: 0 1px 20px 0 rgba(69, 90, 100, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 25px;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  transition: all 0.3s ease;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.navbar-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 15px;
  cursor: pointer;
  color: #6c7293;
  font-size: 18px;
  transition: all 0.3s ease;
}

.navbar-icon:hover {
  color: #8bc34a;
  background-color: rgba(139, 195, 74, 0.1);
}

.navbar .notifications {
  margin-right: 15px;
  font-size: 18px;
  cursor: pointer;
  color: #6c7293;
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.navbar .notifications:hover {
  color: #8bc34a;
  background-color: rgba(139, 195, 74, 0.1);
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #ff5252;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  min-width: 18px;
}

/* Notifications Dropdown */
.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 350px;
  max-height: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  z-index: 1000;
  overflow: hidden;
  margin-top: 10px;
}

.notifications-dropdown::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 15px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

.notifications-header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.notifications-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.mark-all-read {
  background: none;
  border: none;
  color: #8bc34a;
  font-size: 12px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.mark-all-read:hover {
  background-color: rgba(139, 195, 74, 0.1);
}

.notifications-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f8ff;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #8bc34a;
}

.notification-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  font-size: 14px;
}

.notification-icon .fas.fa-utensils {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.notification-icon .fas.fa-paw {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.notification-icon .fas.fa-syringe {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.notification-icon .fas.fa-check-circle {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.notification-icon .fas.fa-exclamation-triangle {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.notification-icon .fas.fa-times-circle {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.notification-icon .fas.fa-info-circle {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 6px;
}

.notification-time {
  font-size: 11px;
  color: #999;
}

.notification-dot {
  width: 8px;
  height: 8px;
  background-color: #8bc34a;
  border-radius: 50%;
  margin-left: 10px;
  flex-shrink: 0;
  margin-top: 4px;
}

.no-notifications {
  padding: 40px 20px;
  text-align: center;
  color: #999;
  font-style: italic;
}

.notifications-footer {
  padding: 15px 20px;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.view-all-notifications {
  width: 100%;
  background: none;
  border: none;
  color: #8bc34a;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.view-all-notifications:hover {
  background-color: rgba(139, 195, 74, 0.1);
}

.mark-all-read-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.mark-all-read-btn:hover {
  background: #0056b3;
}

.test-notification-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.test-notification-btn:hover {
  background: #45a049;
}

.navbar .user-profile {
  width: 38px;
  height: 38px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.navbar .user-profile:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.navbar .user-profile img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Content area */
.content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  height: calc(100vh - var(--navbar-height));
  background-color: #f5f8fa;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
  z-index: 1;
}

.content::-webkit-scrollbar {
  width: 5px;
}

.content::-webkit-scrollbar-track {
  background: transparent;
}

.content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
  }

  .sidebar .logo p,
  .sidebar nav a span,
  .sidebar-footer a span,
  .menu-category span,
  .badge {
    display: none;
  }

  .sidebar nav a i,
  .sidebar-footer a i {
    margin-right: 0;
  }

  .main-content {
    margin-left: 70px;
    width: calc(100% - 70px);
  }

  .sidebar nav a,
  .sidebar-footer a {
    justify-content: center;
    padding: 15px;
  }

  .menu-category {
    height: 20px;
    padding: 0;
  }
}
