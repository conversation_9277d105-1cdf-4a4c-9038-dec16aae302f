<main class="content-container">
  <div class="frame-4">
    <!-- Add New Vaccine Button -->
    <button class="frame-9" (click)="openAddModal()">
      <img class="btn-icon" src="assets/images/icons/Vector.png" alt="Add Vaccine Icon">
      <div class="btntext">Add New Vaccine</div>
    </button>

    <!-- Updating Data Button -->
    <button class="frame-9" (click)="openEditModal()">
      <img class="btn-icon" src="assets/images/icons/iconupdate.png" alt="Update Icon">
      <div class="btntext">Updating Data</div>
    </button>

    <!-- Search Input -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <img class="search-icon" src="assets/images/icons/search.png" alt="Search Icon">
        <input type="text" class="search-field" placeholder="Search" [(ngModel)]="searchTerm" (input)="performSearch()">
      </div>
    </div>

    <!-- Filter Button -->
    <div class="filter-dropdown-container" [class.active]="isFilterDropdownOpen">
      <button class="frame-9 filter-button" (click)="toggleFilterDropdown()">
        <img class="double-left1" src="assets/images/icons/filter.png">
        <div class="btntext">Filter</div>
      </button>
      <div class="filter-dropdown-menu">
        <div class="filter-option" (click)="applyFilter('name')">Name</div>
        <div class="filter-option" (click)="applyFilter('type')">Type</div>
        <div class="filter-option" (click)="applyFilter('dose')">Dose</div>
        <div class="filter-option" (click)="applyFilter('date')">Time To Take</div>
      </div>
    </div>

    <!-- Delete Vaccine Button -->
    <button class="frame-9 delete-btn" (click)="openDeleteModal()">
      <img class="btn-icon" src="assets/images/icons/Trash.png" alt="Delete Icon">
      <div class="btntext">Delete Vaccine</div>
    </button>
  </div>

  <!-- Vaccination Table -->
  <div class="table-container">
     <div class="x">
         <img class="btn-icon" src="assets/images/icons/vaccine.png" alt="">
    <h3>Vaccination</h3>

    </div>
    <table>
      <thead>
        <tr>
       <th> <input type="checkbox" (change)="toggleAllSelections($event)"></th>
          <th>Name</th>
          <th>Type</th>
          <th>Dose</th>
          <th>Time To Take</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let vaccine of filteredRecords" (click)="selectRecord(vaccine)">

          <td><input type="checkbox" [checked]="selectedIds.includes(vaccine.id!)" (change)="toggleSelection(vaccine.id!)"></td>

          <td>{{ vaccine.name }}</td>
          <td>{{ vaccine.type }}</td>
          <td>{{ vaccine.dose }}</td>
          <td>{{ vaccine.date }}</td>
        </tr>

      </tbody>
    </table>
  </div>

  <!-- Add New Vaccine Modal -->
  <div class="choose-add" *ngIf="showAddModal">
    <div class="frame-4 modal add-edit-modal">
      <button class="close-btn" (click)="closeAllModals()"><img src="assets/images/icons/cross.png" alt="Close"></button>
      <div class="frame-5">
        <img class="modal-icon" src="assets/images/icons/vaccine.png" alt="Add Vaccine Icon">
        <div class="text-wrapper-5">Add New Vaccine</div>
      </div>
      <div class="frame-6">
        <div class="form-flex">
          <div class="form-group">
            <label class="text-wrapper-6">Name</label>
            <input type="text" class="data-filled" placeholder="Name" [(ngModel)]="selectedRecord.name">
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Type</label>
            <input type="text" class="data-filled" placeholder="Type" [(ngModel)]="selectedRecord.type">
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Dose</label>
            <input type="text" class="data-filled" placeholder="Dose" [(ngModel)]="selectedRecord.dose">
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Time To Take</label>
            <input type="date" class="data-filled" placeholder="Time To Take" [(ngModel)]="selectedRecord.date">
          </div>
           <div class="frame-8">
          <button class="frame-9 modal-save-btn" (click)="saveRecord()">
            <img class="btn-icon" src="assets/images/icons/save.png" alt="Save Icon">
            <div class="btntext">Save Vaccine</div>
          </button>
        </div>

        </div>
      </div>
    </div>
  </div>

  <!-- Edit Exist Vaccine Modal -->
  <div class="choose-add" *ngIf="showEditModal">
    <div class="frame-4 modal add-edit-modal">
      <button class="close-btn" (click)="closeAllModals()"><img src="assets/images/icons/cross.png" alt="Close"></button>
      <div class="frame-5">
        <img class="modal-icon" src="assets/images/icons/update.png" alt="Edit Vaccine Icon">
        <div class="text-wrapper-5">Edit Exist Vaccine</div>
      </div>
      <div class="frame-6">
        <div class="form-flex">
          <div class="form-group">
            <label class="text-wrapper-6">Name</label>
            <input type="text" class="data-filled" placeholder="Name" [(ngModel)]="selectedRecord.name">


              <button class="frame-9 show-data-btn" (click)="showDataForRecord()">
              <div class="btntext">Show Data</div>
              </button>

          </div>


          <div class="form-group">
            <label class="text-wrapper-6">Type</label>
            <input type="text" class="data-filled" placeholder="Type" [(ngModel)]="selectedRecord.type">
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Dose</label>
            <input type="text" class="data-filled" placeholder="Dose" [(ngModel)]="selectedRecord.dose">
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Time To Take</label>
            <input type="date" class="data-filled" placeholder="Time To Take" [(ngModel)]="selectedRecord.date">
          </div>
        </div>
        <!-- <div class="frame-8"> -->
          <button class="frame-9 modal-save-btn" (click)="updateRecord()">
            <img class="btn-icon" src="assets/images/icons/save.png" alt="Save Icon">
            <div class="btntext">Save Updates</div>
          </button>
        </div>
      </div>
    </div>
  <!-- </div> -->

  <!-- Delete Vaccination Modal -->
  <div class="choose-update-milch" *ngIf="showDeleteModal">
    <div class="frame-4 delete-modal">

      <div class="delete-content">
        <img class="delete-icon" src="assets/images/icons/delete.png" alt="Delete Vaccine Icon">
        <div class="delete-text">Delete Vaccination?</div>
        <div class="delete-buttons">
          <button class="delete-cancel-btn" (click)="closeAllModals()">Cancel</button>
          <button class="delete-confirm-btn" (click)="confirmDelete()">Delete</button>
        </div>
      </div>
    </div>
  </div>
</main>
