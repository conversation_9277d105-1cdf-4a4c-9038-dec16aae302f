import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { of } from 'rxjs';

export interface IngredientPrice {
  id: number;
  price: number;
}

export interface FeedCreate {
  name: string;
  quntity: number;
  ingredientPrice: IngredientPrice[];
}

export interface Ingredient {
  id: number;
  name: string;
  type?: string; // Add type for filtering
}

export interface Category {
  id: number;
  name: string;
}

export interface Feed {
  id?: number;
  count: number;
  feedName: string;
  category?: string;
  proteinPercentage: number;
  tdnPercentage: number;
  type?: string;
  ingredients?: string;
  price?: number;
  items?: { type: number; ingredients: string; price: number }[];
}

export interface FeedApiResponse {
  id: number;
  name: string;
  animalCatgeory: string;
  animalCatgeoryId?: number;
  quntity: number;
  totalProten: number;
  totalTDN: number;
  items?: { type: number; ingredientName?: string; ingredients?: string; price: number }[];
}

@Injectable({
  providedIn: 'root'
})
export class FeedService {
  private apiUrl = 'https://localhost:7174/api';

  constructor(private http: HttpClient) { }

  createFeed(feed: FeedCreate): Observable<any> {
    return this.http.post(`${this.apiUrl}/Feeds`, feed);
  }

  getCategories(): Observable<Category[]> {
    console.log('Calling getCategories API:', `${this.apiUrl}/Feeds/GetCategory`);
    return this.http.get<Category[]>(`${this.apiUrl}/Feeds/GetCategory`);
  }

  getIngredients(): Observable<Ingredient[]> {
    return this.http.get<Ingredient[]>(`${this.apiUrl}/Ingredients`);
  }

  getIngredientsByType(type?: string): Observable<Ingredient[]> {
    if (type) {
      return this.http.get<Ingredient[]>(`${this.apiUrl}/Ingredients?type=${type}`);
    }
    return this.getIngredients();
  }

  getFeeds(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/Feeds`);
  }

  deleteFeed(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/Feeds/${id}`);
  }

  getFeedById(id: number): Observable<FeedApiResponse> {
    return this.http.get<FeedApiResponse>(`${this.apiUrl}/Feeds/${id}`);
  }

  updateFeed(id: number, feed: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/Feeds/${id}`, feed);
  }

  searchFeedsByName(term: string): Observable<FeedApiResponse[]> {
    return this.http.get<FeedApiResponse[]>(`${this.apiUrl}/Feeds/search?term=${term}`).pipe(
      catchError(error => {
        console.error('Error searching feeds', error);
        return of([]);
      })
    );
  }
}
