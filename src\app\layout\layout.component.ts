import { Component, OnInit, HostListener, OnDestroy } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { NotificationService, NotificationItem } from '../services/notification.service';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.css']
})
export class LayoutComponent implements OnInit, OnDestroy {
  currentRoute: string = '';
  private destroy$ = new Subject<void>();

  // Notifications properties
  showNotifications: boolean = false;
  notifications: NotificationItem[] = [];
  unreadNotificationsCount: number = 0;

  constructor(
    private router: Router,
    private notificationService: NotificationService
  ) {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      this.currentRoute = event.url;
    });
  }

  ngOnInit(): void {
    // Subscribe to notifications
    this.notificationService.notifications$
      .pipe(takeUntil(this.destroy$))
      .subscribe(notifications => {
        this.notifications = notifications;
      });

    // Subscribe to unread count
    this.notificationService.unreadCount$
      .pipe(takeUntil(this.destroy$))
      .subscribe(count => {
        this.unreadNotificationsCount = count;
      });

    // Try to load from API, fallback to local data
    this.notificationService.loadFromAPI();

    // Start SignalR connection for real-time notifications
    this.notificationService.startConnection();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Stop SignalR connection
    this.notificationService.stopConnection();
  }

  // Close notifications when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.notifications')) {
      this.showNotifications = false;
    }
  }

  getPageTitle(): string {
    // Extract the page title from the current route
    if (this.currentRoute.includes('/feed')) {
      return 'Feed Management';
    } else if (this.currentRoute.includes('/ingredients')) {
      return 'Ingredients Management';
    } else if (this.currentRoute.includes('/animals')) {
      return 'Animals Management';
    } else if (this.currentRoute.includes('/dairy')) {
      return 'Dairy Management';
    } else if (this.currentRoute.includes('/newborn')) {
      return 'New Born Management';
    } else if (this.currentRoute.includes('/vaccination')) {
      return 'Vaccination Management';
    } else if (this.currentRoute.includes('/reports')) {
      return 'Reports';
    } else if (this.currentRoute.includes('/statistics')) {
      return 'Statistics';
    } else if (this.currentRoute.includes('/settings')) {
      return 'Settings';
    } else if (this.currentRoute.includes('/help')) {
      return 'Help Center';
    } else {
      return 'Dashboard';
    }
  }

  // Notifications methods
  toggleNotifications(): void {
    this.showNotifications = !this.showNotifications;
  }

  markAsRead(notification: NotificationItem): void {
    if (notification.id) {
      // Try API first, fallback to local
      this.notificationService.markAsReadAPI(notification.id).subscribe({
        next: () => {
          this.notificationService.markAsRead(notification.id!);
        },
        error: () => {
          // Fallback to local update
          this.notificationService.markAsRead(notification.id!);
        }
      });
    }
  }

  markAllAsRead(): void {
    // Try API first, fallback to local
    this.notificationService.markAllAsReadAPI().subscribe({
      next: () => {
        this.notificationService.markAllAsRead();
      },
      error: () => {
        // Fallback to local update
        this.notificationService.markAllAsRead();
      }
    });
  }

  getNotificationIcon(type?: string): string {
    switch (type) {
      case 'feed': return 'fas fa-utensils';
      case 'animal': return 'fas fa-paw';
      case 'vaccine': return 'fas fa-syringe';
      case 'success': return 'fas fa-check-circle';
      case 'warning': return 'fas fa-exclamation-triangle';
      case 'error': return 'fas fa-times-circle';
      case 'info':
      default: return 'fas fa-info-circle';
    }
  }

  getTimeAgo(timestamp?: Date | string): string {
    if (!timestamp) return '';

    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `منذ ${diffInDays} يوم`;
  }
}
