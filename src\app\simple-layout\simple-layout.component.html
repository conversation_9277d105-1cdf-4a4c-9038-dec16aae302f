<div class="layout-container" [ngClass]="{'auth-layout': isAuthPage}">
  <!-- Sidebar Component -->
  <div class="side-bar" *ngIf="!isAuthPage">
      <div class="sra-parent">
        <h1 class="sra">SRA</h1>
          <div class="smart-raising-animal">Smart Raising Animal</div>
    </div>
    <div class="side-bar-bottom">
      <div class="side-bar-bottom-inner">
        <img class="item-separator-child" loading="lazy" alt="" src="assets/images/line-1.svg" />
      </div>
      <div class="side-bar-elements">
        <div class="side-bar-options">
          <div class="side-bar-option-parent">
            <a class="vuesaxlinearcategory-2-parent" routerLink="/dashboard" routerLinkActive="active">
              <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt="" src="assets/images/icons/dashboard.png" />

              <div class="animal">Dashboard</div>

            </a>
          </div>
          <div class="side-bar-option-parent-inner">
            <img class="item-separator-child" loading="lazy" alt="" src="assets/images/line-1.svg" />
          </div>
          <div class="side-bar-option-parent1">
            <div class="side-bar-element">
              <a class="side-animal" routerLink="/animals" routerLinkActive="active">
                <img class="bull-icon" loading="lazy" alt="" src="assets/images/icons/bull.png" />
                <div class="animals-wrapper">
                  <div class="animals">Animals</div>
                </div>
              </a>
              <a class="side-animal" routerLink="/dairy" routerLinkActive="active">
                <img class="bull-icon" loading="lazy" alt="" src="assets/images/icons/milch.png" />
                <div class="animals">Dairy</div>
              </a>
              <a class="side-animal" routerLink="/newborn" routerLinkActive="active">
                <img class="cow-1-icon" loading="lazy" alt="" src="assets/images/icons/newborn.png" />
                <div class="animals">New Born</div>
              </a>
              <a class="side-animal" routerLink="/feed" routerLinkActive="active">
                <img class="cow-1-icon" loading="lazy" alt="" src="assets/images/icons/feed.png" />
                <div class="animals">Feed</div>
              </a>
              <a class="side-animal" routerLink="/ingredients" routerLinkActive="active">
                <img class="cow-1-icon" loading="lazy" alt="" src="assets/images/icons/ingrediant icon.png" />
                <div class="animals">Ingredients</div>
              </a>
              <a class="side-animal" routerLink="/vaccination" routerLinkActive="active">
                <img class="cow-1-icon" loading="lazy" alt="" src="assets/images/icons/vaccine.png" />
                <div class="animals">Vaccination</div>
              </a>
              <a class="side-animal" routerLink="/reports" routerLinkActive="active">
                <img class="cow-1-icon" loading="lazy" alt="" src="assets/images/icons/reports.png" />
                <div class="animals">Reports</div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content" [ngClass]="{'full-width': isAuthPage}">
    <!-- Navbar Component -->
    <header class="navbar" *ngIf="!isAuthPage">
      <div class="navbar-left">
        <a class="dashboard">
          <div class="bell-icon-container" (click)="toggleNotificationsDropdown($event)">
            <img class="bell-icon" alt="" src="assets/images/icons/ring.png" />
            <!-- Notification Badge -->
            <span class="notification-badge" *ngIf="unreadNotificationsCount > 0">{{ unreadNotificationsCount }}</span>

            <!-- Notifications Dropdown -->
            <div class="notifications-dropdown" [class.show]="showNotificationsDropdown">
              <div class="notifications-header">
                <h3>الاشعارات</h3>
                <span class="notifications-count" *ngIf="unreadNotificationsCount > 0">{{ unreadNotificationsCount }} جديد</span>
              </div>

              <div class="notifications-list">
                <div class="notification-item"
                     *ngFor="let notification of notifications"
                     [class.unread]="!notification.isRead"
                     (click)="markAsRead(notification)">
                  <div class="notification-icon-wrapper">
                    <i [class]="getNotificationIcon(notification.type)"
                       [ngClass]="{
                         'text-success': notification.type === 'success',
                         'text-warning': notification.type === 'warning',
                         'text-danger': notification.type === 'error',
                         'text-info': notification.type === 'info',
                         'text-primary': notification.type === 'feed',
                         'text-secondary': notification.type === 'animal',
                         'text-purple': notification.type === 'vaccine'
                       }"></i>
                  </div>
                  <div class="notification-content">
                    <div class="notification-title">{{ notification.title }}</div>
                    <div class="notification-message">{{ notification.message }}</div>
                    <div class="notification-time">{{ getTimeAgo(notification.timestamp || notification.createdAt) }}</div>
                  </div>
                  <div class="notification-dot" *ngIf="!notification.isRead"></div>
                </div>

                <div class="no-notifications" *ngIf="notifications.length === 0">
                  لا توجد اشعارات
                </div>
              </div>

              <div class="notifications-footer">
                <button class="mark-all-read-btn" (click)="markAllAsRead()">
                  <i class="fas fa-check-double"></i>
                  تحديد الكل كمقروء
                </button>
                <button class="test-notification-btn" (click)="testNotification()">
                  <i class="fas fa-vial"></i>
                  اختبار
                </button>
              </div>
            </div>
          </div>
        </a>
      </div>
      <div class="navbar-right">
        <div class="user-profile" id="navProfileContainer" (click)="toggleProfileDropdown($event)">
          <img class="male-avatar-portrait-of-a-youn-icon" loading="lazy" alt="" src="assets/images/icons/navbarperson.jpg" />
        <div class="side-pregnant">
            <i class="fas fa-chevron-down"></i>
          </div>
          <!-- Profile Dropdown -->
          <div class="profile-dropdown" [class.show]="showProfileDropdown">
            <a routerLink="/logout" (click)="logout()">Log Out</a>
            <a routerLink="/settings">Settings</a>
            <a routerLink="/about">About</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Content Area -->
    <div class="content-area" [ngClass]="{'auth-content': isAuthPage}">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
