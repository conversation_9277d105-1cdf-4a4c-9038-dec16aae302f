<main class="content-container">
  <div class="frame-4">
    <!-- Make New Feed Button -->
    <button class="frame-9" (click)="openMakeNewFeedModal()">
      <img
        class="btn-icon"
        src="assets/images/icons/feed.png"
        alt="Add Feed Icon"
      />
      <div class="btntext">Make New Feed</div>
    </button>

    <!-- Edit Feed Button -->
    <button class="frame-9" (click)="openSearchFeedModal()">
      <img class="btn-icon" src="assets/images/icons/iconupdate.png" alt="Edit Feed Icon" />
      <div class="btntext">Edit Feed</div>
    </button>

    <!-- Search Input moved here -->
    <div style="display: flex; align-items: center; gap: 10px;">
      <div class="search-input-wrapper">
        <img
          class="search-icon"
          src="assets/images/icons/search.png"
          alt="Search Icon"
        />
        <input
          type="text"
          class="search-field"
          placeholder="Search"
          [(ngModel)]="searchTerm"
          (input)="performSearch()"
        />
      </div>
    </div>

    <!-- Filter Button -->
    <div
      class="filter-dropdown-container"
      [class.active]="isFilterDropdownOpen"
    >
      <button class="frame-9 filter-button" (click)="toggleFilterDropdown()">
        <img class="double-left1" src="assets/images/icons/filter.png" />
        <div class="btntext">Filter</div>
      </button>
      <div class="filter-dropdown-menu">
        <div class="filter-option" (click)="applyFilter('feedName')">
          Feed Name
        </div>
        <div class="filter-option" (click)="applyFilter('category')">
          Category
        </div>
      </div>
    </div>

    <!-- Delete Feed Button -->
    <button class="frame-9 delete-btn" (click)="openDeleteFeedModal()">
      <img
        class="btn-icon"
        src="assets/images/icons/Trash.png"
        alt="Delete Icon"
      />
      <div class="btntext">Delete Feed</div>
    </button>
  </div>

  <!-- Feed Table -->
  <div class="table-container">
   <div class="x">
         <img class="btn-icon" src="assets/images/icons/feed.png" alt="">
    <h3>Feed</h3>

    </div>
    <table>
      <thead>
        <tr>
          <th>
            <input
              type="checkbox"
              [checked]="selectedFeedIds.length === filteredFeeds.length && filteredFeeds.length > 0"
              (change)="toggleAllFeedSelections($event)"
            />
          </th>
          <th>Count</th>
          <th>Name</th>
          <th>Category</th>
          <th>Protein Percentage</th>
          <th>TDN Percentage</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let feed of filteredFeeds" (click)="feed.id !== undefined && openFeedDetailsModal(feed.id)" style="cursor:pointer;">
          <td (click)="$event.stopPropagation()">
            <input
              type="checkbox"
              [checked]="feed.id !== undefined && selectedFeedIds.includes(feed.id)"
              (click)="$event.stopPropagation()"
              (change)="feed.id !== undefined && toggleFeedSelection(feed.id)"
            />
          </td>
          <td>{{ feed.count }}</td>
          <td>{{ feed.feedName }}</td>
          <td>{{ feed.category }}</td>
          <td>{{ feed.proteinPercentage }}%</td>
          <td>{{ feed.tdnPercentage }}%</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Feed Details Modal -->
  <div class="choose-update-milch" *ngIf="showFeedDetailsModal">
    <div class="frame-4 modal add-edit-modal">
      <button class="close-btn" (click)="closeFeedDetailsModal()">
        <img src="assets/images/icons/cross.png" alt="Close" />
      </button>
      <div class="frame-5">
        <img
          class="modal-icon"
          src="assets/images/icons/feed.png"
          alt="Feed Details Icon"
        />
        <div class="text-wrapper-5">تفاصيل العلف</div>
      </div>
      <div class="frame-6">
        <div style="margin-bottom: 16px; display: flex; gap: 32px; align-items: center;">
          <div><strong>البروتين الكلي:</strong> {{ feedDetails?.totalCP }}</div>
          <div><strong>TDN الكلي:</strong> {{ feedDetails?.totalTDN }}</div>
        </div>
        <div class="selected-items-container">
          <table>
            <tr>
              <th>المكون</th>
              <th>النسبة (%)</th>
            </tr>
            <tr *ngFor="let m of feedDetails?.mokawen">
              <td>{{ m.ingredientName }}</td>
              <td>{{ m.percentage }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Make New Feed Modal -->
  <div class="choose-update-milch" *ngIf="showMakeNewFeedModal">
    <div class="frame-4 modal add-edit-modal">
      <button class="close-btn" (click)="closeAllModals()">
        <img src="assets/images/icons/cross.png" alt="Close" />
      </button>
      <div class="frame-5">
        <img
          class="modal-icon"
          src="assets/images/icons/feed.png"
          alt="Make New Feed Icon"
        />
        <div class="text-wrapper-5">Make New Feed</div>
      </div>
      <div class="frame-6">
        <!-- First row: Feed Name and Category -->
        <div class="form-grid">
          <div class="form-group">
            <label class="text-wrapper-6">Name</label>
            <input
              type="text"
              class="data-filled"
              placeholder="Feed Name"
              [(ngModel)]="selectedFeed.feedName"
            />
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Category</label>
            <select class="data-filled" [(ngModel)]="selectedFeed.category">
              <option value="">Select Category</option>
              <option *ngFor="let cat of categories" [value]="cat.id">{{ cat.name }}</option>
            </select>
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Quantity</label>
            <input type="number" class="data-filled" [(ngModel)]="selectedFeed.count" placeholder="Quantity" min="0" />
          </div>
        </div>

        <!-- Second row: Type, Ingredients, Price, and Add button -->
        <div class="form-flex dropdowns-row">
          <div class="form-group">
            <label class="text-wrapper-6"> Type</label>
            <select class="data-filled" [(ngModel)]="selectedType">
              <option value="">Select Type</option>
              <option *ngFor="let t of types" [ngValue]="t.value">{{ t.label }}</option>
            </select>
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Ingredients</label>
            <select class="data-filled" [(ngModel)]="selectedFeed.ingredients">
              <option value="">Component</option>
              <option *ngFor="let ing of filteredIngredients" [value]="ing.name">{{ ing.name }}</option>
            </select>
          </div>
          <div class="form-group price-group">
            <label class="text-wrapper-6">Price</label>
            <input
              type="text"
              class="data-filled price-input"
              [(ngModel)]="selectedFeed.price"
              placeholder="0.00"
            />
          </div>
          <div class="form-group add-button-container">
            <button class="add-button" (click)="addSelectedItem()">
              <img
                src="assets/images/icons/addingred.png"
                alt="Add"
                class="add-icon"
              />
            </button>
          </div>
        </div>

        <div class="selected-items-container">

            <table>
              <tr class="m">
                <!-- <th>ID</th> -->
                <th>type</th>
                <th>ingredients</th>
                <th>price</th>
                <th></th>
              </tr>


               <tr *ngFor="let item of selectedItems" class="selected-item">
                <!-- <td>{{ getIngredientIdByName(item.ingredients) }}</td> -->
                <td>{{ typeNames[item.type] }}</td>
                <td>{{ item.ingredients }}</td>
                <td>{{ item.price }}</td>
                <td>   <button class="remove-button" (click)="removeItem(item)"><img src="assets/images/icons/delingred.png"></button></td>
              </tr>


            </table>


          </div>
        </div>

        <div class="frame-8">
          <button class="frame-9 modal-save-btn" (click)="submitNewFeed()">
            <img
              class="btn-icon"
              src="assets/images/icons/save.png"
              alt="Save Feed Icon"
            />
            <div class="btntext">Save Feed</div>
          </button>
        </div>
      </div>
    </div>

  <!-- Edit Exist Feed Modal -->
  <div class="choose-update-milch" *ngIf="showEditExistFeedModal">
    <div class="frame-4 modal add-edit-modal">
      <button class="close-btn" (click)="closeAllModals()">
        <img src="assets/images/icons/cross.png" alt="Close" />
      </button>
      <div class="frame-5">
        <img
          class="modal-icon"
          src="assets/images/icons/update.png"
          alt="Edit Feed Icon"
        />
        <div class="text-wrapper-5">Edit Exist Feed</div>
      </div>
      <div class="frame-6">
        <!-- Search by Feed Name -->
        <div class="form-group" *ngIf="!isFeedDataLoaded" style="display: flex; gap: 10px; align-items: center; margin-bottom: 20px;">
          <input
            type="text"
            class="data-filled"
            placeholder="ادخل اسم العلف للبحث"
            [(ngModel)]="searchFeedName"
            (keyup.enter)="searchFeedByName()"
            style="width: 220px;"
          />
          <button class="frame-9" (click)="searchFeedByName()">
            <div class="btntext">بحث</div>
          </button>
        </div>

        <!-- باقي النموذج يظهر فقط بعد نجاح البحث -->
        <ng-container *ngIf="isFeedDataLoaded">
          <div class="frame-6">
            <!-- First row: Feed Name and Category -->
            <div class="form-grid">
              <div class="form-group">
                <label class="text-wrapper-6">Name</label>
                <input
                  type="text"
                  class="data-filled"
                  placeholder="Feed Name"
                  [(ngModel)]="selectedFeed.feedName"
                />
              </div>
              <div class="form-group">
                <label class="text-wrapper-6">Category</label>
                <select class="data-filled" [(ngModel)]="selectedFeed.category">
                  <option value="">Select Category</option>
                  <option *ngFor="let cat of categories" [value]="cat.id">{{ cat.name }}</option>
                </select>
              </div>
              <div class="form-group">
                <label class="text-wrapper-6">Quantity</label>
                <input type="number" class="data-filled" [(ngModel)]="selectedFeed.count" placeholder="Quantity" min="0" />
              </div>
            </div>

            <!-- Second row: Type, Ingredients, Price, and Add button -->
            <div class="form-flex dropdowns-row">
              <div class="form-group">
                <label class="text-wrapper-6"> Type</label>
                <select class="data-filled" [(ngModel)]="selectedType">
                  <option value="">Select Type</option>
                  <option *ngFor="let t of types" [ngValue]="t.value">{{ t.label }}</option>
                </select>
              </div>
              <div class="form-group">
                <label class="text-wrapper-6">Ingredients</label>
                <select class="data-filled" [(ngModel)]="selectedFeed.ingredients">
                  <option value="">Component</option>
                  <option *ngFor="let ing of filteredIngredients" [value]="ing.name">{{ ing.name }}</option>
                </select>
              </div>
              <div class="form-group price-group">
                <label class="text-wrapper-6">Price</label>
                <input
                  type="text"
                  class="data-filled price-input"
                  [(ngModel)]="selectedFeed.price"
                  placeholder="0.00"
                />
              </div>
              <div class="form-group add-button-container">
                <button class="add-button" (click)="addSelectedItem()">
                  <img
                    src="assets/images/icons/addingred.png"
                    alt="Add"
                    class="add-icon"
                  />
                </button>
              </div>
            </div>

            <div class="selected-items-container">
              <table>
                <tr class="m">
                  <!-- <th>ID</th> -->
                  <th>type</th>
                  <th>ingredients</th>
                  <th>price</th>
                  <th></th>
                </tr>
                <tr *ngFor="let item of selectedItems" class="selected-item">
                  <!-- <td>{{ getIngredientIdByName(item.ingredients) }}</td> -->
                  <td>{{ typeNames[item.type] }}</td>
                  <td>{{ item.ingredients }}</td>
                  <td>{{ item.price }}</td>
                  <td>
                    <button class="remove-button" (click)="removeItem(item)"><img src="assets/images/icons/delingred.png"></button>
                  </td>
                </tr>
              </table>
            </div>

            <div class="frame-8">
              <button class="frame-9 modal-save-btn" (click)="submitUpdateFeed()">
                <img
                  class="btn-icon"
                  src="assets/images/icons/save.png"
                  alt="Save Feed Icon"
                />
                <div class="btntext">Save Updates</div>
              </button>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>

  <div class="choose-update-milch" *ngIf="showDeleteFeedModal">
    <div class="frame-4 modaldelete">
      <div class="delete-content">
        <img class="delete-icon" src="assets/images/icons/delete.png" alt="" />
        <div class="delete-text">Delete Feed?</div>
        <div class="delete-buttons">
          <button class="delete-cancel-btn" (click)="closeAllModals()">
            Cancel
          </button>
          <button class="delete-confirm-btn" (click)="confirmDeleteFeed()">
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Search Feed Modal -->
  <div class="choose-update-milch" *ngIf="showSearchFeedModal">
    <div class="frame-4 modal add-edit-modal">
      <button class="close-btn" (click)="closeAllModals()">
        <img src="assets/images/icons/cross.png" alt="Close" />
      </button>
      <div class="frame-5">
        <img
          class="modal-icon"
          src="assets/images/icons/update.png"
          alt="Edit Feed Icon"
        />
        <div class="text-wrapper-5">Edit Feed</div>
      </div>
      <div class="frame-6">
        <!-- Search by Feed Name with AutoComplete -->
        <div class="form-group">
          <label class="text-wrapper-6">Feed Name</label>
          <div class="autocomplete-container">
            <div style="display: flex; gap: 10px; align-items: center;">
              <input
                type="text"
                class="data-filled"
                placeholder="Type feed name to search"
                [(ngModel)]="searchTerm"
                (input)="searchFeeds()"
                (keyup.enter)="searchFeeds()"
                style="flex: 1;"
              />
              <button
                class="frame-9"
                (click)="searchFeeds()"
                style="padding: 8px 16px; min-width: auto;"
              >
                <img
                  class="btn-icon"
                  src="assets/images/icons/search.png"
                  alt="Search Icon"
                  style="width: 16px; height: 16px;"
                />
                <div class="btntext">Search</div>
              </button>
              <button
                class="frame-9"
                (click)="showAllFeeds()"
                style="padding: 8px 16px; min-width: auto; background-color: #28a745;"
              >
                <div class="btntext">Show All</div>
              </button>
            </div>
            <div class="autocomplete-results" *ngIf="searchResults.length > 0">
              <div
                class="autocomplete-item"
                *ngFor="let feed of searchResults"
                (click)="selectFeed(feed)"
              >
                {{ feed.name }}
              </div>
            </div>
            <div *ngIf="isLoading && searchTerm.length >= 1" class="no-results">
              Searching...
            </div>
            <div *ngIf="!isLoading && searchTerm.length >= 1 && searchResults.length === 0" class="no-results">
              No feeds found matching "{{ searchTerm }}"
            </div>
          </div>
        </div>

        <!-- Feed Edit Form - appears after selection -->
        <div *ngIf="selectedFeed.id" class="edit-feed-form">
          <div class="form-grid">
            <div class="form-group">
              <label class="text-wrapper-6">Name</label>
              <input
                type="text"
                class="data-filled"
                placeholder="Feed Name"
                [(ngModel)]="selectedFeed.feedName"
              />
            </div>
            <div class="form-group">
              <label class="text-wrapper-6">Category</label>
              <select class="data-filled" [(ngModel)]="selectedFeed.category">
                <option value="">Select Category</option>
                <option *ngFor="let cat of categories" [value]="cat.id">{{ cat.name }}</option>
              </select>
            </div>
            <div class="form-group">
              <label class="text-wrapper-6">Quantity</label>
              <input
                type="number"
                class="data-filled"
                placeholder="Quantity"
                [(ngModel)]="selectedFeed.count"
              />
            </div>
          </div>

          <!-- Ingredients Table -->
          <div class="ingredients-section">
            <h3>Ingredients</h3>
            
            <!-- Add New Ingredient -->
            <div class="add-ingredient-form">
              <div class="form-grid">
                <div class="form-group">
                  <label class="text-wrapper-6">Type</label>
                  <select class="data-filled" [(ngModel)]="newIngredient.type">
                    <option value="" disabled selected>Select Type</option>
                    <option *ngFor="let type of types" [value]="type.value">{{ type.label }}</option>
                  </select>
                </div>
                <div class="form-group">
                  <label class="text-wrapper-6">Ingredient</label>
                  <select class="data-filled" [(ngModel)]="newIngredient.ingredients">
                    <option value="" disabled selected>Select Ingredient</option>
                    <option *ngFor="let ing of filteredIngredients" [value]="ing.name">{{ ing.name }}</option>
                  </select>
                </div>
                <div class="form-group">
                  <label class="text-wrapper-6">Price</label>
                  <input
                    type="number"
                    class="data-filled"
                    placeholder="Price"
                    [(ngModel)]="newIngredient.price"
                  />
                </div>
                <button class="add-btn" (click)="addIngredient()">Add</button>
              </div>
            </div>
            
            <!-- Ingredients List -->
            <div class="selected-items-container">
              <table>
                <tr>
                  <th>Type</th>
                  <th>Ingredient</th>
                  <th>Price</th>
                  <th>Action</th>
                </tr>
                <tr *ngFor="let item of selectedItems">
                  <td>{{ typeNames[item.type] }}</td>
                  <td>{{ item.ingredients }}</td>
                  <td>{{ item.price }}</td>
                  <td>
                    <button class="remove-button" (click)="removeItem(item)">
                      <img src="assets/images/icons/delingred.png">
                    </button>
                  </td>
                </tr>
              </table>
            </div>
          </div>

          <!-- Save Button -->
          <div class="frame-8">
            <button class="frame-9 modal-save-btn" (click)="submitUpdateFeed()">
              <img
                class="btn-icon"
                src="assets/images/icons/save.png"
                alt="Save Feed Icon"
              />
              <div class="btntext">Save Changes</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
