import { Compo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON><PERSON>, HostListener, ElementRef } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, Data, RouterModule } from '@angular/router';
import { Subscription, Subject } from 'rxjs';
import { filter, map, switchMap, takeUntil } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { CommonModule } from '@angular/common';
import { NotificationService, NotificationItem } from '../services/notification.service';


interface CustomRouteData extends Data {
  title?: string;
  isAuthPage?: boolean;
}

@Component({
  selector: 'app-simple-layout',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './simple-layout.component.html',
  styleUrls: ['./simple-layout.component.css']
})


export class SimpleLayoutComponent implements OnInit, OnDestroy {
  isAuthPage: boolean = false;
  pageTitle: string = '';
  userName: string = '<PERSON>'; // Example user name
  userRole: string = 'Admin'; // Example user role
  notifications: NotificationItem[] = [];
  unreadNotificationsCount: number = 0;
  showNotificationsDropdown = false;// Property for notifications dropdown
  showProfileDropdown: boolean = false; // New property for profile dropdown
  private routerSubscription: Subscription;
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private authService: AuthService,
    private elementRef: ElementRef,
    private notificationService: NotificationService
  ) {
    this.routerSubscription = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => this.activatedRoute),
      map(route => {
        while (route.firstChild) route = route.firstChild;
        return route;
      }),
      filter(route => route.outlet === 'primary'),
      switchMap(route => route.data)
    ).subscribe((data: CustomRouteData) => {
      this.pageTitle = data.title || 'Dashboard';
      this.isAuthPage = data.isAuthPage || false;
    });
  }

  ngOnInit(): void {
    // Subscribe to notifications
    this.notificationService.notifications$
      .pipe(takeUntil(this.destroy$))
      .subscribe(notifications => {
        console.log('Notifications updated in simple-layout:', notifications);
        this.notifications = notifications;
      });

    // Subscribe to unread count
    this.notificationService.unreadCount$
      .pipe(takeUntil(this.destroy$))
      .subscribe(count => {
        console.log('Unread count updated in simple-layout:', count);
        this.unreadNotificationsCount = count;
      });

    // Try to load from API, fallback to local data
    this.notificationService.loadFromAPI();

    // Start SignalR connection for real-time notifications
    this.notificationService.startConnection();
  }

  ngOnDestroy(): void {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }

    this.destroy$.next();
    this.destroy$.complete();

    // Stop SignalR connection
    this.notificationService.stopConnection();
  }

  getPageTitle(): string {
    return this.pageTitle;
  }

  getUserRole(): string {
    return this.userRole;
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  toggleNotificationsDropdown(event: Event): void {
    this.showNotificationsDropdown = !this.showNotificationsDropdown;
    this.showProfileDropdown = false; // Close profile dropdown when opening notifications
    event.stopPropagation(); // Prevent document click from immediately closing it
  }

  toggleProfileDropdown(event: Event): void {
    this.showProfileDropdown = !this.showProfileDropdown;
    this.showNotificationsDropdown = false; // Close notifications dropdown when opening profile
    event.stopPropagation(); // Prevent document click from immediately closing it
  }

  @HostListener('document:click', ['$event'])
  onClick(event: Event): void {
    // Close dropdowns if click is outside their respective containers
    if (!this.elementRef.nativeElement.querySelector('.bell-icon-container')?.contains(event.target)) {
      this.showNotificationsDropdown = false;
    }
    if (!this.elementRef.nativeElement.querySelector('#navProfileContainer')?.contains(event.target)) {
      this.showProfileDropdown = false;
    }
  }

  // Notification methods
  markAsRead(notification: NotificationItem): void {
    console.log('🔔 Marking notification as read:', notification);
    if (notification.id) {
      // Try API first, fallback to local
      this.notificationService.markAsReadAPI(notification.id).subscribe({
        next: () => {
          console.log('✅ API call successful, updating local state');
          this.notificationService.markAsRead(notification.id!);
        },
        error: (error) => {
          console.log('❌ API call failed, using local fallback:', error);
          // Fallback to local update
          this.notificationService.markAsRead(notification.id!);
        }
      });
    } else {
      console.log('⚠️ Notification has no ID, updating locally only');
      this.notificationService.markAsRead(notification.id!);
    }
  }

  markAllAsRead(): void {
    console.log('🔔 Marking all notifications as read');
    // Try API first, fallback to local
    this.notificationService.markAllAsReadAPI().subscribe({
      next: () => {
        console.log('✅ Mark all API call successful, updating local state');
        this.notificationService.markAllAsRead();
      },
      error: (error) => {
        console.log('❌ Mark all API call failed, using local fallback:', error);
        // Fallback to local update
        this.notificationService.markAllAsRead();
      }
    });
  }

  getNotificationIcon(type?: string): string {
    switch (type) {
      case 'feed': return 'fas fa-utensils';
      case 'animal': return 'fas fa-paw';
      case 'vaccine': return 'fas fa-syringe';
      case 'success': return 'fas fa-check-circle';
      case 'warning': return 'fas fa-exclamation-triangle';
      case 'error': return 'fas fa-times-circle';
      case 'info':
      default: return 'fas fa-info-circle';
    }
  }

  getTimeAgo(timestamp?: Date | string): string {
    if (!timestamp) return '';

    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `منذ ${diffInDays} يوم`;
  }

  // Test method for debugging
  testNotification(): void {
    console.log('Testing notification from simple-layout...');
    this.notificationService.testBackendNotification();
  }
}
