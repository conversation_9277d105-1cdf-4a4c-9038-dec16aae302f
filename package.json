{"name": "app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "api": "ng-openapi-gen --config openapi/base/configs.json"}, "private": true, "dependencies": {"@angular/animations": "16.2.12", "@angular/common": "16.2.12", "@angular/compiler": "16.2.12", "@angular/core": "16.2.12", "@angular/forms": "16.2.12", "@angular/platform-browser": "16.2.12", "@angular/platform-browser-dynamic": "16.2.12", "@angular/router": "16.2.12", "@microsoft/signalr": "^8.0.7", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "chart.js": "4.4.0", "ng-openapi-gen": "^0.53.0", "ng2-charts": "4.0.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "16.2.0", "@angular/cli": "16.2.12", "@angular/compiler-cli": "16.2.12", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}