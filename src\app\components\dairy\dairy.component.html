<main class="content-container">
  <div class="frame-4">
    <!-- Record Milk Production Today Button -->
    <button class="frame-9" (click)="openAddModal()">
      <img class="btn-icon" src="assets/images/icons/record.png" alt="">
      <div class="btntext"> Milk Production Today</div>
    </button>

    <!-- Add New Dairy Record Button -->


    <!-- Updating Data Button -->
    <button class="frame-9" (click)="openEditModal(null)">
      <img class="btn-icon" src="assets/images/icons/iconupdate.png" alt="">
      <div class="btntext">Updating Data</div>
    </button>

    <!-- Search Input and Dropdown -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <img class="search-icon" src="assets/images/icons/search.png" alt="">
        <input type="text" class="search-field" placeholder="البحث في جميع الخصائص..." [(ngModel)]="searchTerm" (input)="searchDairies()">
      </div>
    
       
      </div>
    

    <!-- Filter Button -->
    <div class="filter-dropdown-container" [class.active]="isFilterDropdownOpen">
      <button class="frame-9 filter-button" (click)="toggleFilterDropdown()">
        <img class="double-left1" src="assets/images/icons/filter.png">
        <div class="btntext">Filter</div>
      </button>
      <div class="filter-dropdown-menu">
        <div class="filter-option" (click)="applyFilter('herdNumber')">Herd Number</div>
        <div class="filter-option" (click)="applyFilter('weight')">Weight</div>
        <div class="filter-option" (click)="applyFilter('type')">Type</div>
        <div class="filter-option" (click)="applyFilter('milk')">Milk Production</div>
      </div>
    </div>

    <!-- Delete Dairy Record Button -->
    <button class="frame-9 delete-btn" (click)="openDeleteModal()">
      <img class="btn-icon" src="assets/images/icons/Trash.png" alt="">
      <div class="btntext">Delete Animal</div>
    </button>
  </div>

  <div class="table-container">

   <div class="x">
         <img class="btn-icon" src="assets/images/icons/milch.png" alt="">
    <h3>Dairy</h3>

    </div>
  <!-- Dairy Table -->
  <div class="dairy-table">
    <table>
      <thead>
        <tr>
        <th>
            <input
              type="checkbox"
              [checked]="
                selectedAnimalIds.length === filteredDairies.length &&
                filteredDairies.length > 0
              "
              (change)="toggleAllAnimalSelections($event)"
            />
          </th>
          <th>Code</th>
          <th>Type</th>
          <th>Herd Number</th>
          <th>Milk Production</th>
          <th>Fat Percentage</th>
          <th>Weight</th>
          <th>Date of weight</th>
          <th>Statue Fortification</th>
          <th>DateFertilization</th>
          <th>ExpectedDate</th>
          <th>Healthcare</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let record of filteredDairies">
             <td>
            <input
              type="checkbox"
              [checked]="selectedAnimalIds.includes(record.id!)"
              (click)="$event.stopPropagation()"
              (change)="toggleAnimalSelection(record.id!)"
            />
          </td>
          <td>{{ record.code }}</td>
          <td>{{ getTypeDisplayName(record.type) }}</td>
          <td>{{ record.noFamily }}</td>
          <td>{{ record.milk }}</td>
          <td>{{ record.fatPercentage || '—' }}</td>
          <td>{{ record.weight }}</td>
          <td>{{ record.weightDate }}</td>
          <td>{{ getFertilizationStatusDisplayName(record.statuForitification) }}</td>
          <td>{{ record.dateFertilization || '—' }}</td>
          <td>{{ record.expectedDate || '—' }}</td>
          <td>{{ record.description || '—' }}</td>
        </tr>
      </tbody>
    </table>
  </div>
  </div>



  <div class="choose-update-milch" *ngIf="showEditModal">
  <div class="edit-modal">
    
    <button class="close-btn" (click)="closeModals()">
      <img src="/assets/images/icons/cross.png" alt="" />
    </button>

    <div class="frame-5">
      <img class="modal-icon" src="/assets/images/icons/update.png" alt="" />
      <div class="text-wrapper-5">Updating Data in Dairy</div>
    </div>

    <!-- ✅ البحث عن الحيوان بالكود -->
    <div *ngIf="!selectedDairy?.code"  class="form-group show-modal ">
      <label class="text-wrapper-6">Code</label>
      <input
        type="text"
        class="data-filled"
        placeholder="Code"
        [(ngModel)]="searchCode"
        (keyup.enter)="findAnimalByCode()"
      />
      <button class="frame-9 modal-save-btn" (click)="findAnimalByCode()">
        <div class="btntext">ShowData</div>
      </button>
    </div>

    <!-- ✅ عرض النموذج للتعديل بعد العثور على الحيوان -->
    <form *ngIf="selectedDairy?.code" (ngSubmit)="updateDairy()">
      <div class="form-grid">
        <div class="form-group">
          <label for="editCode" class="text-wrapper-6">Code</label>
          <input type="text" id="editCode" class="data-filled" placeholder="Code" [(ngModel)]="selectedDairy.code" name="editCode" required />
        </div>

        <div class="form-group">
          <label for="editType" class="text-wrapper-6">Type</label>
          <div class="radio-group">
            <div class="raddiv">
              <input type="radio" class="radio" name="editType" [value]="1" [(ngModel)]="selectedDairy.type" />
              <label>Dairy</label>
            </div>
            <div class="raddiv">
              <input type="radio" class="radio" name="editType" [value]="3" [(ngModel)]="selectedDairy.type" />
              <label>Dry</label>
            </div>
           
          </div>

        </div>

        <div class="form-group">
          <label for="editWeight" class="text-wrapper-6">Weight</label>
          <input type="number" id="editWeight" class="data-filled" placeholder="Weight" [(ngModel)]="selectedDairy.weight" name="editWeight" />
        </div>

        <div class="form-group">
          <label for="editDateOfArtificialInsemination" class="text-wrapper-6">DateFertilization</label>
          <input type="date" id="editDateOfArtificialInsemination" class="data-filled" placeholder="DD/MM/YY" [(ngModel)]="selectedDairy.dateFertilization" name="editDateOfArtificialInsemination" />
        </div>

        <div class="form-group">
          <label for="editDateOfWeight" class="text-wrapper-6">Date of weight</label>
          <input type="date" id="editDateOfWeight" class="data-filled" placeholder="DD/MM/YY" [(ngModel)]="selectedDairy.weightDate" name="editDateOfWeight" />
        </div>

        <div class="form-group">
          <label for="editStatueOfInsemination" class="text-wrapper-6">statuForitification</label>
          <div class="radio-group">
            <div class="raddiv">
              <input type="radio" class="radio" name="statueOfInsemination" [value]="0" [(ngModel)]="selectedDairy.statuForitification" />
              <label> Not Pregnant</label>
            </div>
            <div class="raddiv">
              <input type="radio" class="radio" name="statueOfInsemination" [value]="1" [(ngModel)]="selectedDairy.statuForitification" />
              <label> Is Pregnant</label>
            </div>
            
          </div>
        </div>

        <div class="form-group">
          <label for="editHerdNumber" class="text-wrapper-6">Herd Number</label>
          <input type="text" id="editHerdNumber" class="data-filled" placeholder="Herd Number" [(ngModel)]="selectedDairy.noFamily" name="editHerdNumber" />
        </div>

        <div class="form-group">
          <label for="editHealthcareNote" class="text-wrapper-6">Healthcare note</label>
          <textarea id="editHealthcareNote" class="textarea" placeholder="Health Care" [(ngModel)]="selectedDairy.description" name="editHealthcareNote"></textarea>
        </div>

        <div class="form-group">
          <label for="editExpectedDateOfCalving" class="text-wrapper-6">ExpectedDate</label>
          <input type="date" id="editExpectedDateOfCalving" class="data-filled" placeholder="DD/MM/YYYY" [(ngModel)]="selectedDairy.expectedDate" name="editExpectedDateOfCalving" />
        </div>

        <div class="form-group">
          <label for="editTakenVaccinations" class="text-wrapper-6">Taken Vaccinations</label>
          <input type="text" id="editTakenVaccinations" class="data-filled" placeholder="Taken Vaccinations" [(ngModel)]="selectedDairy.description" name="editTakenVaccinations" />
        </div>
      </div>

      <div class="modal-actions">
        <button type="submit" class="editsave-btn">
          <img class="btn-icon" src="/assets/images/icons/save.png" alt="" />
          Save Updates
        </button>
      </div>
    </form>
  </div>
</div>

  <!-- Delete Modal -->
  <div class="choose-update-milch" *ngIf="showDeleteModal">
    <div class="modaldelete">

      <div class="delete-content">
        <img class="delete-icon" src="assets/images/icons/delete.png" alt="">
        <div class="delete-text">Delete Animal?</div>
        <div class="delete-buttons">
          <button class="delete-cancel-btn" (click)="closeModals()">Cancel</button>
          <button class="delete-confirm-btn" (click)="confirmDeleteDairies()">Delete</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Record Milk Production Today Modal -->
  <div class="choose-update-milch" *ngIf="showAddModal">
    <div class="frame-recordmilk ">
      <button class="close-btn" (click)="closeModals()"><img src="/assets/images/icons/cross.png" alt=""></button>
      <div class="frame-5">
        <img class="modal-icon" src="/assets/images/icons/milch.png" alt="">
        <div class="text-wrapper-5">Record Milk Production</div>
      </div>
      
      <!-- Search for animal by code first -->
      <div *ngIf="!selectedDairy?.code || selectedDairy?.code === ''" class="form-group show-modal">
        <label class="text-wrapper-6">Code</label>
        <input
          type="text"
          class="data-filled"
          placeholder="Code"
          [(ngModel)]="searchCode"
          (keyup.enter)="findAnimalByCode()"
        />
        <button class="frame-9 modal-save-btn" (click)="findAnimalByCode()">
          <div class="btntext">ShowData</div>
        </button>
      </div>

      <!-- Show milk production form after finding animal -->
      <form *ngIf="selectedDairy?.code && selectedDairy?.code !== ''" (ngSubmit)="addDairy()" #milkForm="ngForm">
        <div class="form-flex">
          <div class="form-group">
            <label for="recordMilkProduction">Milk Production (Liters)</label>
            <input type="number" id="recordMilkProduction" class="data-filled" placeholder="Milk Production" [(ngModel)]="selectedDairy.milk" name="recordMilkProduction" step="0.1" min="0" required>
          </div>
          <div class="form-group">
            <label for="recordFatPresentence">Fat Percentage (%)</label>
            <input type="number" id="recordFatPresentence" class="data-filled" placeholder="Fat Percentage" [(ngModel)]="selectedDairy.fatPercentage" name="recordFatPresentence" step="0.1" min="0" max="100" required>
          </div>
        </div>
        <div class="modal-actions">
          <button type="submit" class="save-btn" [disabled]="!milkForm.valid">
            <img class="btn-icon" src="/assets/images/icons/save.png" alt="">
            Save Milk Production
          </button>
        </div>
      </form>
    </div>
  </div>
</main>
