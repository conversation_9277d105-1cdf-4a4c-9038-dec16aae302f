body {
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
}

.settings-container {
  background: #fff;
  border-radius: 8px;
  max-width: 600px;
  margin: 32px auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 0;
  border: 1px solid #e5e5e5;
}

.settings-header {
  display: flex;
  align-items: center;
  padding: 24px 24px 0 24px;
  gap: 10px;
}

.settings-icon {
  width: 28px;
  height: 28px;
}

.settings-title {
  color: #226622;
  font-size: 22px;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.divider {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 12px 0 0 0;
}

.section-block {
  padding: 0 24px 0 24px;
  margin-top: 18px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: #222;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.access-btn, .edit-btn {
  background: #fff;
  border: 1px solid #d6d6d6;
  border-radius: 16px;
  padding: 4px 18px;
  font-size: 15px;
  font-weight: 400;
  color: #444;
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
}
.access-btn {
  margin-right: 2px;
}
.access-btn:hover, .edit-btn:hover {
  background: #f5f5f5;
  border-color: #bdbdbd;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
}

.info-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
  min-height: 44px;
  padding: 0;
}
.info-row:last-child {
  border-bottom: none;
}

.label {
  flex: 1.2;
  color: #444;
  font-size: 16px;
  font-weight: 400;
  padding: 10px 0 10px 0;
}
.value {
  flex: 2;
  color: #222;
  font-size: 17px;
  font-weight: 500;
  text-align: left;
  padding: 10px 0 10px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.profile-picture {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
  border: none;
  box-shadow: 0 1px 4px rgba(0,0,0,0.07);
}

.small-btn {
  background: #fff;
  border: 1px solid #d6d6d6;
  border-radius: 16px;
  padding: 2px 16px;
  font-size: 14px;
  color: #444;
  font-weight: 400;
  cursor: pointer;
  margin-left: 0;
  transition: background 0.2s, border 0.2s;
}
.small-btn:hover {
  background: #f5f5f5;
  border-color: #bdbdbd;
}

@media (max-width: 700px) {
  .settings-container {
    max-width: 98vw;
    padding: 0;
  }
  .section-block {
    padding: 0 8px;
  }
  .settings-header {
    padding: 16px 8px 0 8px;
  }
}

.modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.13);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-modal {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.13);
  width: 420px;
  max-width: 98vw;
  padding: 0 0 32px 0;
  position: relative;
  animation: fadeIn 0.2s;
}

.edit-title {
  font-size: 20px;
  font-weight: 700;
  color: #226622;
  margin: 24px 0 0 32px;
}

.edit-profile-pic-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 18px;
  position: relative;
}

.edit-profile-pic {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  object-fit: cover;
  border: 2.5px dashed #4caf50;
  background: #f7f7f7;
}

.edit-pic-btn {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 50%;
  border: 1.5px solid #4caf50;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-top: -18px;
}
.camera-icon {
  color: #4caf50;
  font-size: 18px;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
  margin: 0 32px;
}

.edit-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.edit-input-group label {
  color: #888;
  font-size: 15px;
  margin-bottom: 2px;
}
.edit-input-group input {
  border: 1.5px solid #bdbdbd;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 17px;
  color: #222;
  background: #fff;
  outline: none;
  transition: border 0.2s;
}
.edit-input-group input:focus {
  border-color: #4caf50;
}

.change-password-link {
  color: #4caf50;
  font-size: 15px;
  text-align: left;
  margin-top: -8px;
  margin-bottom: 8px;
  cursor: pointer;
}

.edit-actions {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  margin-top: 18px;
}
.save-btn {
  background: #27c127;
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 10px 36px 10px 24px;
  font-size: 18px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(39,193,39,0.08);
  transition: background 0.2s;
}
.save-btn i {
  font-size: 20px;
}
.save-btn:hover {
  background: #1ea11e;
}
.back-btn {
  background: none;
  border: none;
  color: #1ea1e1;
  font-size: 32px;
  cursor: pointer;
  margin-right: auto;
  margin-left: 0;
  padding: 0 8px 0 0;
  transition: color 0.2s;
}
.back-btn:hover {
  color: #0b6fa4;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.97); }
  to { opacity: 1; transform: scale(1); }
}

.give-access-title {
  font-size: 18px;
  font-weight: 700;
  color: #226622;
  margin: 24px 0 18px 0;
  text-align: left;
}
.give-access-form {
  margin-top: 0;
  gap: 18px;
}
.give-access-btn {
  background: #18b118;
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 10px 36px;
  font-size: 18px;
  font-weight: 500;
  display: block;
  margin: 24px auto 0 auto;
  box-shadow: 0 2px 8px rgba(39,193,39,0.08);
  transition: background 0.2s;
  cursor: pointer;
}
.give-access-btn:hover {
  background: #0e8c0e;
}

.close-x {
  position: absolute;
  top: 14px;
  right: 18px;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: #fff;
  color: #27c127;
  font-size: 26px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(39,193,39,0.07);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border 0.2s, background 0.2s, color 0.2s;
  border: 2px solid transparent;
  z-index: 10;
}
.close-x:hover {
  border: 2px solid #27c127;
  background: #f6fff6;
  color: #179117;
}

.engineers-section {
  margin: 32px 0 0 0;
  padding: 0 24px;
}
.engineers-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.engineers-list li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f2f2f2;
}
.delete-engineer-btn {
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 18px;
  cursor: pointer;
  transition: color 0.2s;
}
.delete-engineer-btn:hover {
  color: #b71c1c;
} 